use std::collections::{
    HashMap,
    HashSet,
};
use std::fs::{
    File,
    create_dir_all,
    remove_dir_all,
    remove_file,
};
use std::io::{
    <PERSON><PERSON><PERSON><PERSON>,
    BufReader,
    BufWriter,
    Write,
};
use std::path::Path;
use std::process::Command;
use std::thread;
use std::time::Instant;

use chrono::{
    NaiveDate,
    NaiveTime,
};
use clap::Parser as ClapParser;
use dashmap::DashMap;
use indexmap::IndexMap;
use itoa::Buffer as ItoaBuffer;  // fast integer to string
use num_format::{
    Locale,
    ToFormattedString,
};
use mysql::{
    OptsBuilder,
    Pool,
    prelude::Queryable,
};
use rayon::iter::{
    IntoParallelRefIterator,
    ParallelIterator,
};
use serde_json;

use eterna::rahavard::{
    convert_second,
    save_log,
    sort_dict,
    to_tilda,
};

use eterna::utils_database::{
    get_size_of_database,
    get_tables_and_sizes,
};

use eterna::utils_classes::{
    MYSQLConfig,
    MYSQLValue,
    Parser,
    SnortConfig,
    SnortParser,
};

use eterna::utils::{
    all_values_are_0,
    create_name_of_database,
    create_name_of_index,
    create_path_of_infile,
    evenly_sized_batches,
    get_no_of_infiles,
    hms_to_hourkey,
    separator,
};

use eterna::utils_ip::{
    is_private,
};

use eterna::utils_parsers::{
    parse_ln,
    ConfigType,
};

// 16MB buffer for optimal I/O performance
const FILE_BUFFER_SIZE: usize = 16 * 1024 * 1024;

#[derive(ClapParser, Debug)]
#[command(author, version, about)]
struct Args {
    #[arg(long = "source-log")]
    source_log: String,
    // /FOO/BAR/BAZ/2025-06-13--Fri.log

    #[arg(long = "log-date")]
    log_date: String,
    // 2020-01-02

    #[arg(long = "host-name")]
    host_name: String,
    // custom-host-name

    #[arg(long = "command")]
    command: String,
    // parse-snort

    #[arg(long = "already-accomplished", num_args = 0..)]
    already_accomplished: Vec<String>,
    // [] OR ["Sensor-1", "Sensor-2", ...]

    #[arg(long = "sensor-list-of-names", num_args = 1..)]
    sensor_list_of_names: Vec<String>,
    // ["Sensor-1", "Sensor-2", "Sensor-3", ...]

    #[arg(long = "sensor-list-of-names-and-addresses", num_args = 1..)]
    sensor_list_of_names_and_addresses: Vec<String>,
    // ["Sensor-1", "***********", "Sensor-2", "***********", ...]

    #[arg(long = "sensor-dict-of-addresses-and-names")]
    sensor_dict_of_addresses_and_names: String,
    // "{\"***********\": \"Sensor-1\", \"***********\": \"Sensor-2\", ...}"

    #[arg(long = "force", num_args = 0)]
    #[arg(default_value_t = false)]
    force: bool,
    // true/false
}

// #[inline(always)]
// fn trim_line(line: &mut String) {
//     // more efficient trimming using byte operations
//     let bytes = unsafe { line.as_bytes_mut() };
//     let mut end = bytes.len();

//     while end > 0 && (bytes[end - 1] == b'\n' || bytes[end - 1] == b'\r') {
//         end -= 1;
//     }

//     unsafe {
//         line.as_mut_vec().set_len(end);
//     }
// }

fn write_into_infile(
    start_of_chunk: usize,
    end_of_chunk: usize,
    infile_path: &str,
    infile_index: usize,
    no_of_infiles: usize,
    instance_rows: &[Vec<String>],
    terminated_by: &str,
    enclosed_by: &str,
    command: &str,
    host_name: &str,
    log_file: &str,
) {
    save_log(command, host_name, log_file, &format!("  writing into {} ({}/{}): {} -> {}", infile_path, infile_index, no_of_infiles, start_of_chunk.to_formatted_string(&Locale::en), end_of_chunk.to_formatted_string(&Locale::en)), true).unwrap();

    let file = match File::create(infile_path) {
        Ok(f) => f,
        Err(e) => {
            save_log(command, host_name, log_file, &format!("Error creating infile {}: {}", infile_path, e), true).unwrap();
            return;
        }
    };

    let mut writer = BufWriter::with_capacity(512 * 1024, file);  // 512 KB buffer
    let mut id_buf = ItoaBuffer::new();
    let mut output = Vec::with_capacity(1024 * instance_rows.len());  // preallocate large buffer

    let mut row_id = start_of_chunk;
    for instance_row in instance_rows.iter() {
        row_id += 1;

        // ('a', 'b', 'c') -> 1-*@*-a-*@*-b-*@*-c
        output.extend_from_slice(enclosed_by.as_bytes());
        output.extend_from_slice(id_buf.format(row_id).as_bytes());
        output.extend_from_slice(enclosed_by.as_bytes());
        //
        for cell in instance_row {
            output.extend_from_slice(terminated_by.as_bytes());
            output.extend_from_slice(enclosed_by.as_bytes());
            output.extend_from_slice(cell.as_bytes());
            output.extend_from_slice(enclosed_by.as_bytes());
        }
        //
        output.push(b'\n');

        // flush if buffer is getting too large
        if output.len() > 8 * 1024 * 1024 {
            writer.write_all(&output);
            output.clear();
        }
    }

    // final flush
    writer.write_all(&output);

    writer.flush();
}

fn main() {
    let args = Args::parse();

    // string -> dict
    let sensor_dict_of_addresses_and_names: HashMap<String, String> = serde_json::from_str(&args.sensor_dict_of_addresses_and_names).unwrap();

    // list -> set for O(1) lookup
    let already_accomplished: HashSet<String> = args.already_accomplished.into_iter().collect();

    // ------

    // create dictionary of instances
    let sensor_names_and_instances: DashMap<String, SnortParser> = DashMap::new();
    for s_n in &args.sensor_list_of_names {
        sensor_names_and_instances.insert(
            s_n.clone(),
            SnortParser::new(
                SnortConfig::SLUG.value().value_string(),
                args.log_date.to_string(),
                s_n.to_string(),
            ),
        );
    }

    println!("parsing...");
    let parse_start = Instant::now();

    // open file with larger buffer for better I/O performance
    let file = File::open(&args.source_log)
               .expect(&format!("Failed to open source log: {}", args.source_log));
    let mut reader = BufReader::with_capacity(FILE_BUFFER_SIZE, file);

    // process file in chunks to avoid loading entire file into memory
    let pool_chunksize = MYSQLConfig::POOL_CHUNKSIZE.value().value_int();

    loop {
        let mut chunk = Vec::with_capacity(pool_chunksize);

        // read chunk of lines with pre-allocated string buffer
        let mut line_buffer = String::with_capacity(512);  // typical log line length
        for _ in 0..pool_chunksize {
            line_buffer.clear();
            match reader.read_line(&mut line_buffer) {
                Ok(0) => break,  // EOF
                Ok(_) => {
                    // trim_line(&mut line_buffer);  // remove newline character
                    chunk.push(line_buffer.clone());
                }
                Err(e) => panic!("Error reading line: {}", e),
            }
        }

        if chunk.is_empty() {
            // EOF reached
            break;
        }

        // process chunk in parallel with optimized aggregation
        let local_results: HashMap<String, Vec<Vec<String>>> = chunk
            .par_iter()
            .filter_map(|line| {
                let (sensor_name, parsed_ln) = parse_ln(
                    line.trim(),
                    ConfigType::Snort,
                    &args.sensor_list_of_names_and_addresses,
                    &sensor_dict_of_addresses_and_names,
                );

                // early return for accomplished sensors (O(1) lookup)
                if let Some(ref name) = sensor_name {
                    if already_accomplished.contains(name) {
                        return None;
                    }
                }

                match (sensor_name, parsed_ln) {
                    (Some(name), Some(row)) => Some((name, row)),
                    _ => None,
                }
            })
            .fold(
                || HashMap::with_capacity(args.sensor_list_of_names.len()),
                |mut acc, (name, row)| {
                    acc.entry(name).or_insert_with(|| Vec::with_capacity(2000)).push(row);
                    acc
                }
            )
            .reduce(
                || HashMap::with_capacity(args.sensor_list_of_names.len()),
                |mut acc1, acc2| {
                    for (k, mut v) in acc2 {
                        match acc1.entry(k) {
                            std::collections::hash_map::Entry::Occupied(mut e) => {
                                e.get_mut().append(&mut v);
                            }
                            std::collections::hash_map::Entry::Vacant(e) => {
                                e.insert(v);
                            }
                        }
                    }
                    acc1
                }
            );

        // collect results into sensor instances
        for (name, rows) in local_results {
            if let Some(mut instance) = sensor_names_and_instances.get_mut(&name) {
                instance.rows.extend(rows);
            }
        }
    }

    let parse_duration = parse_start.elapsed();
    println!("parsed in {} seconds ({})", parse_duration.as_secs().to_formatted_string(&Locale::en), convert_second(parse_duration.as_secs() as f64, false));


    for entry in sensor_names_and_instances.iter() {
        let sensor_start = Instant::now();

        let sensor_name  = entry.key();
        let mut instance = entry.value().clone();

        let dest_dir          = format!("{}/{}/{}", SnortConfig::get_logs_parsed_dir(), sensor_name, args.log_date);
        let accomplished_file = format!("{}/{}-accomplished.log", dest_dir, args.log_date);
        let log_file          = format!("{}/{}.log", dest_dir, args.log_date);

        let database_name = create_name_of_database(&SnortConfig::SLUG.value().value_string(), &args.log_date, sensor_name);

        // ################################################

        // remove and/or create dest_dir
        if Path::new(&dest_dir).exists() {
            let mut should_rm_dest_dir = false;

            if args.force {
                should_rm_dest_dir = true;
            } else {
                if Path::new(&accomplished_file).exists() {
                    println!("{} for sensor {} is already parsed. skipping", args.log_date, sensor_name);
                    continue;
                } else {
                    should_rm_dest_dir = true;
                }
            };

            if should_rm_dest_dir {
                println!("removing {}", to_tilda(&dest_dir));
                remove_dir_all(&dest_dir);

                println!("creating {}", to_tilda(&dest_dir));
                create_dir_all(&dest_dir);
            }
        } else {
            println!("creating {}", to_tilda(&dest_dir));
            create_dir_all(&dest_dir);
        }

        // ################################################

        // START __inserting_into_dbs__

        let db_creds = OptsBuilder::new()
            .ip_or_hostname(Some(MYSQLConfig::MYSQL_HOST.value().value_string()))
            .user(Some(MYSQLConfig::MYSQL_MASTER.value().value_string()))
            .pass(Some(MYSQLConfig::MYSQL_MASTER_PASSWD.value().value_string()));

        // drop/create database
        match Pool::new(db_creds) {
            Ok(pool) => {
                match pool.get_conn() {
                    Ok(mut conn) => {
                        save_log(&args.command, &args.host_name, &log_file, &format!("dropping database {}", database_name), true).unwrap();
                        conn.query_drop(format!("DROP DATABASE IF EXISTS {};", database_name)).unwrap();

                        save_log(&args.command, &args.host_name, &log_file, &format!("creating database {}", database_name), true).unwrap();
                        conn.query_drop(format!("CREATE DATABASE {};", database_name)).unwrap();
                    }
                    Err(e) => {
                        save_log(&args.command, &args.host_name, &log_file, &format!("Error getting database connection: {}", e), true).unwrap();
                    }
                }
            }
            Err(e) => {
                save_log(&args.command, &args.host_name, &log_file, &format!("Error creating database pool: {}", e), true).unwrap();
            }
        }

        // ################################################
        // *table

        // __CHUNKED_INFILE__

        let no_of_infiles = get_no_of_infiles(instance.no_of_rows());

        if no_of_infiles > 0 {
            save_log(&args.command, &args.host_name, &log_file, &format!("{} rows will be inserted into database", instance.no_of_rows().to_formatted_string(&Locale::en)), true).unwrap();

            let db_creds = OptsBuilder::new()
                .ip_or_hostname(Some(MYSQLConfig::MYSQL_HOST.value().value_string()))
                .user(Some(MYSQLConfig::MYSQL_MASTER.value().value_string()))
                .pass(Some(MYSQLConfig::MYSQL_MASTER_PASSWD.value().value_string()))
                .db_name(Some(database_name.clone()));

            // create table and insert data
            match Pool::new(db_creds) {
                Ok(pool) => {
                    match pool.get_conn() {
                        Ok(mut conn) => {
                            save_log(&args.command, &args.host_name, &log_file, &format!("creating table {}", SnortConfig::get_table_name()), true).unwrap();
                            conn.query_drop(format!("CREATE TABLE {} ({});", SnortConfig::get_table_name(), SnortConfig::DB_COLUMNS.value().value_string())).unwrap();

                            conn.query_drop("SET UNIQUE_CHECKS=0;").unwrap();
                            conn.query_drop("SET FOREIGN_KEY_CHECKS=0;").unwrap();
                            conn.query_drop("START TRANSACTION;").unwrap();

                            save_log(&args.command, &args.host_name, &log_file, &format!("{} infiles will be created", no_of_infiles), true).unwrap();

                            // in each loop:
                            // STEP 1: create n infiles at the same time
                            // STEP 2: insert the n infiles into database one at a time
                            let batches = evenly_sized_batches(no_of_infiles as isize, None);
                            for (batch_index, batch) in batches.iter().enumerate() {
                                let batch_index = batch_index + 1;
                                save_log(&args.command, &args.host_name, &log_file, &format!("batch {}: writing into {} infiles", batch_index, batch.len()), true).unwrap();

                                let mut processes = vec![];
                                let mut infile_paths = vec![];

                                // STEP 1: create n infiles at the same time
                                for &infile_index in batch {
                                    let infile_path = create_path_of_infile(&database_name, &SnortConfig::get_table_name(), Some(infile_index));
                                    let start_of_chunk = MYSQLConfig::INFILE_CHUNKSIZE.value().value_int() * (infile_index - 1);
                                    let end_of_chunk = start_of_chunk + MYSQLConfig::INFILE_CHUNKSIZE.value().value_int();

                                    infile_paths.push(infile_path.clone());


                                    // calculating end_of_chunk__real to avoid the error:
                                    //   thread 'main' panicked at src/bin/parse-*.rs:421:77:
                                    //   range end index 5000000 out of range for slice of length 1121362
                                    let end_of_chunk__real = usize::min(start_of_chunk + MYSQLConfig::INFILE_CHUNKSIZE.value().value_int(), instance.no_of_rows());
                                    //
                                    // only borrow the exact slice this thread will need
                                    let instance_rows_slice = &instance.rows[start_of_chunk..end_of_chunk__real];
                                    let instance_rows_vec = instance_rows_slice.to_vec();  // ✅ small clone

                                    let command_clone   = args.command.clone();
                                    let host_name_clone = args.host_name.clone();
                                    let log_file_clone  = log_file.clone();


                                    let pr = thread::spawn(move || {
                                        write_into_infile(
                                            start_of_chunk,
                                            instance_rows_vec.len(),  // passed as end_of_chunk in python
                                            &infile_path,
                                            infile_index,
                                            no_of_infiles,
                                            &instance_rows_vec,
                                            &MYSQLConfig::TERMINATED_BY.value().value_string(),
                                            &MYSQLConfig::ENCLOSED_BY.value().value_string(),
                                            &command_clone,
                                            &host_name_clone,
                                            &log_file_clone,
                                        );
                                    });

                                    processes.push(pr);
                                }

                                // wait for all threads to complete
                                for p in processes {
                                    p.join();
                                }

                                // STEP 2: insert the n infiles into database one at a time
                                save_log(&args.command, &args.host_name, &log_file, &format!("batch {}: inserting into {} from {} infiles", batch_index, SnortConfig::get_table_name(), infile_paths.len()), true).unwrap();

                                for (infile_idx, infile_path) in infile_paths.iter().enumerate() {
                                    if !Path::new(infile_path).exists() {
                                        continue;
                                    }

                                    save_log(&args.command, &args.host_name, &log_file, &format!("  inserting from {}", infile_path), true).unwrap();

                                    let infile_statement = format!(
                                        r#"{} "{}"
                                        INTO TABLE {}
                                        FIELDS TERMINATED BY "{}"
                                        ENCLOSED BY '{}'
                                        LINES TERMINATED BY "\n"
                                        (ID,{});"#,
                                        MYSQLConfig::get_infile_statement(),
                                        infile_path,
                                        SnortConfig::get_table_name(),
                                        &MYSQLConfig::TERMINATED_BY.value().value_string(),
                                        &MYSQLConfig::ENCLOSED_BY.value().value_string(),
                                        &SnortConfig::DB_KEYS.value().value_string(),
                                    );

                                    // JUMP_1
                                    // conn.query_drop(infile_statement).unwrap();

                                    // NOTE running database query through shell
                                    //      instead of mysql::query_drop() in JUMP_1
                                    //      because mysql ≥ 25.0 (set in Cargo.toml)
                                    //      has dropped support for "LOAD DATA LOCAL INFILE" entirely
                                    let infile_status = Command::new("mysql")
                                        .args([
                                            // for using infiles located in /tmp.
                                            // no harm to be on development.
                                            // __TODO__ check if required in production
                                            "--local-infile=1",

                                            "-D", &database_name,
                                            "-h", &MYSQLConfig::MYSQL_HOST.value().value_string(),
                                            "-u", &MYSQLConfig::MYSQL_MASTER.value().value_string(),
                                            &format!("-p{}", MYSQLConfig::MYSQL_MASTER_PASSWD.value().value_string()),  // do NOT "-p{}" -> "-p'{}'"
                                            "-e", &infile_statement,
                                        ])
                                        .status();
                                    //
                                    match infile_status {
                                        Ok(exit_status) => {
                                            if !exit_status.success() {
                                                save_log(&args.command, &args.host_name, &log_file, &format!("infile statement failed for: {}", infile_path), true).unwrap();
                                            }
                                        }
                                        Err(e) => {
                                            save_log(&args.command, &args.host_name, &log_file, &format!("Error executing infile statement for {}: {}", infile_path, e), true).unwrap();
                                        }
                                    }
                                }

                                // commit after loop
                                save_log(&args.command, &args.host_name, &log_file, "  committing...", true).unwrap();
                                conn.query_drop("COMMIT;").unwrap();

                                for infile_path in &infile_paths {
                                    // remove infile
                                    save_log(&args.command, &args.host_name, &log_file, &format!("  removing {}", infile_path), true).unwrap();
                                    remove_file(infile_path).unwrap();
                                }
                            }

                            let tablenames_and_keys_for_index = SnortConfig::TABLENAMES_AND_KEYS_FOR_INDEX.value().value_tuple();

                            for (t_n_, ky_) in tablenames_and_keys_for_index {
                                let index_name = create_name_of_index(&ky_);
                                save_log(&args.command, &args.host_name, &log_file, &format!("creating index {}", index_name), true).unwrap();

                                // __CREATE_INDEX__
                                // could not add IF NOT EXISTS
                                // because on remote it is not valid
                                // as of mysql 8.4.0 and throws error.
                                // for that, we have to use try block
                                // in order to avoid the error:
                                // OperationalError(1061, "Duplicate key name '<NAME_OF_INDEX>'")
                                let create_index_statement = format!(
                                    "CREATE INDEX {} ON {} ({}({})) USING {};",
                                    index_name,
                                    t_n_,
                                    ky_,
                                    MYSQLConfig::INDEX_PREFIX_LENGTH.value().value_int(),
                                    MYSQLConfig::INDEX_TYPE.value().value_string(),
                                );

                                // NOTE keep match structure because it is
                                //      equivalent to try block in the python version
                                match conn.query_drop(&create_index_statement) {
                                    Ok(_) => {
                                        conn.query_drop("COMMIT;").unwrap();
                                    }
                                    Err(e) => {
                                        save_log(&args.command, &args.host_name, &log_file, &format!("Error creating index {}: {:?}", index_name, e), true).unwrap();
                                    }
                                }
                            }
                        }
                        Err(e) => {
                            save_log(&args.command, &args.host_name, &log_file, &format!("Error getting database connection: {}", e), true).unwrap();
                        }
                    }
                }
                Err(e) => {
                    save_log(&args.command, &args.host_name, &log_file, &format!("Error creating database pool: {}", e), true).unwrap();
                }
            }
        }

        // ################################################
        // *_and_counts

        // __INDEXES_ONE_OFF__
        // the indexes in parse-<APP_SLUG>.py (this script) are lower by 1
        // compared to its hourly counterpart (i.e. hourly-parse-<APP_SLUG>.py).
        // in hourly-parse-<APP_SLUG>.py,
        // instance.rows are directly read from *table in database
        // meaning ID column is also included
        // so we have to increment indexes by 1
        // to get the right columns

        save_log(&args.command, &args.host_name, &log_file, "preparing *_and_counts", true).unwrap();

        let mut hmses_and_counts: HashMap<String, i32> = HashMap::new();

        for row in &instance.rows {
            let hms = &row[1];  // 00:49:51
            *hmses_and_counts.entry(hms.clone()).or_insert(0) += 1;

            *instance.gidsids_and_counts.entry(row[2].clone()).or_insert(0) += 1;
            *instance.descriptions_and_counts.entry(row[3].clone()).or_insert(0) += 1;
            *instance.classifications_and_counts.entry(row[4].clone()).or_insert(0) += 1;
            *instance.priorities_and_counts.entry(row[5].clone()).or_insert(0) += 1;
            *instance.protocols_and_counts.entry(row[6].clone()).or_insert(0) += 1;
            *instance.source_ips_and_counts.entry(row[7].clone()).or_insert(0) += 1;
            *instance.source_ports_and_counts.entry(row[8].clone()).or_insert(0) += 1;
            *instance.destination_ips_and_counts.entry(row[9].clone()).or_insert(0) += 1;
            *instance.destination_ports_and_counts.entry(row[10].clone()).or_insert(0) += 1;
        }

        // hmses_and_counts looks like this:
        // {
        //     "02:14:56": 2,
        //     "04:20:34": 14,
        //     "03:15:05": 7,
        //     ...
        // }

        if let Ok(base_date) = NaiveDate::parse_from_str(&args.log_date, "%Y-%m-%d") {
            for (hms, count) in hmses_and_counts {
                // {'00:49:51': 12, '02:59:55': 1182, ...}
                // ->
                // {'00:00 - 00:59': 416787, '01:00 - 01:59': 416167, ...}
                *instance.times_and_counts.entry(hms_to_hourkey(&hms)).or_insert(0) += count;

                // -----

                // log_date hms -> millisecond
                // (2023-05-12 00:00:26 -> 1624973400000)
                if let Ok(time) = NaiveTime::parse_from_str(&hms, "%H:%M:%S") {
                    let datetime = base_date.and_time(time);
                    let milliseconds = datetime.and_utc().timestamp_millis();

                    // convert to string
                    let mut buffer_ = itoa::Buffer::new();
                    let milliseconds__str = buffer_.format(milliseconds);

                    instance.milliseconds_and_counts.insert(milliseconds__str.to_owned(), count);
                }
            }
        } else {
            // fallback: base_date not valid. only update times_and_counts
            for (hms, count) in hmses_and_counts {
                *instance.times_and_counts.entry(hms_to_hourkey(&hms)).or_insert(0) += count;
            }
        }

        // ################################################

        instance.levels_and_counts.insert("Critical".to_string(), 0);
        instance.levels_and_counts.insert("Warning".to_string(),  0);
        instance.levels_and_counts.insert("Low".to_string(),      0);
        instance.levels_and_counts.insert("Very Low".to_string(), 0);

        let classifications__criticals = SnortConfig::CLASSIFICATIONS__CRITICALS.value().value_list();
        let classifications__warnings  = SnortConfig::CLASSIFICATIONS__WARNINGS.value().value_list();
        let classifications__lows      = SnortConfig::CLASSIFICATIONS__LOWS.value().value_list();
        let classifications__very_lows = SnortConfig::CLASSIFICATIONS__VERY_LOWS.value().value_list();

        for (classification_, count_) in &instance.classifications_and_counts {
            if classifications__criticals.contains(classification_) {
                *instance.levels_and_counts.get_mut("Critical").unwrap() += count_;
            } else if classifications__warnings.contains(classification_) {
                *instance.levels_and_counts.get_mut("Warning").unwrap() += count_;
            } else if classifications__lows.contains(classification_) {
                *instance.levels_and_counts.get_mut("Low").unwrap() += count_;
            } else if classifications__very_lows.contains(classification_) {
                *instance.levels_and_counts.get_mut("Very Low").unwrap() += count_;
            }
        }

        // ################################################
        // *toptable

        let db_creds = OptsBuilder::new()
            .ip_or_hostname(Some(MYSQLConfig::MYSQL_HOST.value().value_string()))
            .user(Some(MYSQLConfig::MYSQL_MASTER.value().value_string()))
            .pass(Some(MYSQLConfig::MYSQL_MASTER_PASSWD.value().value_string()))
            .db_name(Some(database_name.clone()));

        match Pool::new(db_creds) {
            Ok(pool) => {
                match pool.get_conn() {
                    Ok(mut conn) => {
                        for (dictionary, table_name, key) in vec![
                            // dictionary                             table_name                 column/key
                            (&instance.times_and_counts,             "timetoptable",            "Time"),
                            (&instance.gidsids_and_counts,           "gidsidtoptable",          "`GID:SID`"),
                            (&instance.descriptions_and_counts,      "descriptiontoptable",     "Description"),
                            (&instance.classifications_and_counts,   "classificationtoptable",  "Classification"),
                            (&instance.priorities_and_counts,        "prioritytoptable",        "Priority"),
                            (&instance.protocols_and_counts,         "protocoltoptable",        "Protocol"),
                            (&instance.source_ips_and_counts,        "sourceiptoptable",        "`Source IP`"),
                            (&instance.source_ports_and_counts,      "sourceporttoptable",      "`Source Port`"),
                            (&instance.destination_ips_and_counts,   "destinationiptoptable",   "`Destination IP`"),
                            (&instance.destination_ports_and_counts, "destinationporttoptable", "`Destination Port`"),

                            (&instance.milliseconds_and_counts,      "millisecondtoptable",     "Millisecond"),
                            (&instance.levels_and_counts,            "leveltoptable",           "Level"),
                        ] {
                            if (key == "Time" || key == "Millisecond") && all_values_are_0(dictionary) {
                                continue;
                            }

                            if dictionary.is_empty() {
                                continue;
                            }

                            let sorted_dict = if key == "Time" || key == "Millisecond" {
                                sort_dict(dictionary, "key", false)
                            } else {
                                sort_dict(dictionary, "value", true)
                            };

                            let table_columns = format!(
                                "ID {}, {} {}, Count {}",
                                MYSQLConfig::ID_DATA_TYPE.value().value_string(),
                                key,
                                MYSQLConfig::DEFAULT_DATA_TYPE.value().value_string(),
                                MYSQLConfig::COUNT_DATA_TYPE.value().value_string(),
                            );
                            let table_keys = format!("{},Count", key);
                            let table_marks = "?,?";

                            save_log(&args.command, &args.host_name, &log_file, &format!("creating table {}", table_name), true).unwrap();
                            conn.query_drop(format!("CREATE TABLE {} ({});", table_name, table_columns)).unwrap();

                            save_log(&args.command, &args.host_name, &log_file, &format!("inserting {} rows into {}", sorted_dict.len().to_formatted_string(&Locale::en), table_name), true).unwrap();
                            conn.query_drop("START TRANSACTION;").unwrap();

                            // save `format!(...)` and `sorted_dict.iter().collect()` as variables instead of using them
                            // directly in `conn.exec_batch(...)` — this improves performance by reducing
                            // temporaries and aiding type inference.
                            let insert_statement = format!("INSERT INTO {} ({}) VALUES ({});", table_name, table_keys, table_marks);
                            let tuplized: Vec<_> = sorted_dict.iter().collect();  // dict -> tuple
                            conn.exec_batch(&insert_statement, tuplized).unwrap();
                        }

                        conn.query_drop("COMMIT;").unwrap();
                    }
                    Err(e) => {
                        save_log(&args.command, &args.host_name, &log_file, &format!("Error getting database connection: {}", e), true).unwrap();
                    }
                }
            }
            Err(e) => {
                save_log(&args.command, &args.host_name, &log_file, &format!("Error creating database pool: {}", e), true).unwrap();
            }
        }

        // ################################################

        // __INDEXES_ONE_OFF__
        let src_ips_and_dest_ips__tuple: Vec<(String, String)> = instance.rows
            .iter()
            .map(|row| (row[7].clone(), row[9].clone()))
            .collect();
        // [
        //     # source ip      destination ip
        //     ('***********', '*******'),
        //     ('***********', '*******'),
        //     ...
        // ]


        let mut dest_ips_and_src_ips__dict: HashMap<String, Vec<String>> = HashMap::new();
        for (src_ip, dest_ip) in src_ips_and_dest_ips__tuple {
            if is_private(&dest_ip) {
                continue;
            }

            // commented because on some servers
            // even when destination ips are public
            // source ips my be public ips too
            // if !is_private(&src_ip) {
            //     continue;
            // }

            dest_ips_and_src_ips__dict
                .entry(dest_ip)
                .or_insert_with(Vec::new)
                .push(src_ip);
        }

        let mut dest_ips_and_src_ips_counts: HashMap<String, HashMap<String, i32>> = HashMap::new();
        for (dest_ip, src_ips) in dest_ips_and_src_ips__dict {
            // processing src_ips list in 3 steps:

            // 1/3: list -> dictionary
            //      (equivalent of applying Counter() upon src_ips list)
            let mut src_ip_counts: HashMap<String, i32> = HashMap::new();
            for src_ip in src_ips {
                *src_ip_counts.entry(src_ip).or_insert(0) += 1;
            }

            // 2/3: sort the dictionary
            let src_ip_counts__sorted = sort_dict(&src_ip_counts, "value", true);

            // 3/3: convert the dictionary back to HashMap
            //      because when writing to database,
            //      we need to convert it to JSON string
            let src_ip_counts__sorted__hashmap: HashMap<String, i32> = src_ip_counts__sorted.into_iter().collect();

            dest_ips_and_src_ips_counts.insert(dest_ip, src_ip_counts__sorted__hashmap);
        }
        // {
        //     '*******': {'***********': 1030},
        //     '*******': {'***********': 2},
        //     '*******': {'***********': 1660, '***********': 992, ...},
        //     ...
        // }


        // __SORT_DICTIONARY_MANUALLY__
        // sort dest_ips_and_src_ips_counts manually.
        // since we can't use sort_dict with HashMap values,
        // we'll sort manually
        let mut dest_ips_and_src_ips_counts__sorted: Vec<_> = dest_ips_and_src_ips_counts.into_iter().collect();
        dest_ips_and_src_ips_counts__sorted.sort_by(|a, b| a.0.cmp(&b.0));  // sort by key (Destination IP)
        let dest_ips_and_src_ips_counts: IndexMap<String, HashMap<String, i32>> = dest_ips_and_src_ips_counts__sorted.into_iter().collect();

        // IP        column contains destination ips
        // IPsCounts column contains source ips + counts
        let table_name = "visitorsofiptable";
        let table_columns = format!(
            "ID          {},
             IP          {},
             IPsCounts   {},
             `No of IPs` {}",
            MYSQLConfig::ID_DATA_TYPE.value().value_string(),
            MYSQLConfig::DEFAULT_DATA_TYPE.value().value_string(),
            MYSQLConfig::DEFAULT_DATA_TYPE.value().value_string(),
            MYSQLConfig::COUNT_DATA_TYPE.value().value_string(),
        );
        let table_keys = "IP,IPsCounts,`No of IPs`";
        let table_marks = "?,?,?";

        let db_creds = OptsBuilder::new()
            .ip_or_hostname(Some(MYSQLConfig::MYSQL_HOST.value().value_string()))
            .user(Some(MYSQLConfig::MYSQL_MASTER.value().value_string()))
            .pass(Some(MYSQLConfig::MYSQL_MASTER_PASSWD.value().value_string()))
            .db_name(Some(database_name.clone()));

        match Pool::new(db_creds) {
            Ok(pool) => {
                match pool.get_conn() {
                    Ok(mut conn) => {
                        save_log(&args.command, &args.host_name, &log_file, &format!("creating table {}", table_name), true).unwrap();
                        conn.query_drop(format!("CREATE TABLE {} ({});", table_name, table_columns)).unwrap();

                        save_log(&args.command, &args.host_name, &log_file, &format!("inserting {} rows into {}", dest_ips_and_src_ips_counts.len().to_formatted_string(&Locale::en), table_name), true).unwrap();
                        conn.query_drop("START TRANSACTION;").unwrap();

                        for (k, v) in dest_ips_and_src_ips_counts.iter() {
                            // convert HashMap to JSON string.
                            // equivalent to python's dumps(v) (__USING_DUMPS_LOADS__)
                            let v__json = serde_json::to_string(v).unwrap();

                            conn.exec_drop(
                                &format!("INSERT INTO {} ({}) VALUES ({});", table_name, table_keys, table_marks),
                                (k, &v__json, v.len() as i32)
                            ).unwrap();
                        }

                        conn.query_drop("COMMIT;").unwrap();
                    }
                    Err(e) => {
                        save_log(&args.command, &args.host_name, &log_file, &format!("Error getting database connection: {}", e), true).unwrap();
                    }
                }
            }
            Err(e) => {
                save_log(&args.command, &args.host_name, &log_file, &format!("Error creating database pool: {}", e), true).unwrap();
            }
        }

        // ################################################
        instance.truncate_all();
        // ################################################

        save_log(&args.command, &args.host_name, &log_file, &format!("database: {}, {}", database_name, get_size_of_database(&database_name, true).unwrap()), true).unwrap();
        save_log(&args.command, &args.host_name, &log_file, &format!("tables: {:?}", get_tables_and_sizes(&database_name, true, "value", true, true).unwrap()), true).unwrap();

        // ################################################

        // create accomplished_file
        save_log(&args.command, &args.host_name, &accomplished_file, "accomplished", true).unwrap();

        let sensor_duration = sensor_start.elapsed();
        save_log(&args.command, &args.host_name, &log_file, &format!("accomplished in {} seconds ({})", sensor_duration.as_secs().to_formatted_string(&Locale::en), convert_second(sensor_duration.as_secs() as f64, false)), true).unwrap();

        println!("{}", separator());

        // END __inserting_into_dbs__
    }
}
